#!/usr/bin/env node

/**
 * 简单映射测试 - 验证修复后的视频映射
 */

console.log('🔧 简单映射测试');
console.log('===============');

// 测试映射数据
const expectedMappings = {
    'INDEX': 'love-website/home',
    'MEETINGS': 'love-website/meetings', 
    'ANNIVERSARY': 'love-website/anniversary',
    'MEMORIAL': 'love-website/memorial',
    'TOGETHER_DAYS': 'love-website/together-days'
};

// 读取hybrid-cdn-manager.js文件内容
const fs = require('fs');
const hybridManagerContent = fs.readFileSync('hybrid-cdn-manager.js', 'utf8');

// 提取videoMappings对象
const mappingMatch = hybridManagerContent.match(/this\.videoMappings\s*=\s*{([\s\S]*?)};/);
if (!mappingMatch) {
    console.log('❌ 无法找到videoMappings定义');
    process.exit(1);
}

console.log('✅ 找到videoMappings定义');

// 检查每个页面的映射
let testsPassed = 0;
let totalTests = 0;

for (const [pageKey, expectedCloudinary] of Object.entries(expectedMappings)) {
    totalTests++;
    
    // 检查大写键是否存在
    const upperCasePattern = new RegExp(`'${pageKey}'\\s*:\\s*{[^}]*cloudinary:\\s*'([^']*)'`, 'i');
    const match = hybridManagerContent.match(upperCasePattern);
    
    if (match && match[1] === expectedCloudinary) {
        console.log(`✅ ${pageKey}: 映射正确 (${match[1]})`);
        testsPassed++;
    } else if (match) {
        console.log(`❌ ${pageKey}: 映射错误 - 找到: ${match[1]}, 期望: ${expectedCloudinary}`);
    } else {
        console.log(`❌ ${pageKey}: 未找到映射`);
    }
}

// 检查cloudinary-load-balancer.js
console.log('\n📊 检查CloudinaryLoadBalancer映射...');

const loadBalancerContent = fs.readFileSync('cloudinary-load-balancer.js', 'utf8');
let loadBalancerPassed = 0;

for (const [pageKey, expectedPublicId] of Object.entries(expectedMappings)) {
    const pattern = new RegExp(`'${pageKey}'\\s*:\\s*{[^}]*publicId:\\s*'([^']*)'`, 'i');
    const match = loadBalancerContent.match(pattern);
    
    if (match && match[1] === expectedPublicId) {
        console.log(`✅ ${pageKey}: 配置正确 (${match[1]})`);
        loadBalancerPassed++;
    } else if (match) {
        console.log(`❌ ${pageKey}: 配置错误 - 找到: ${match[1]}, 期望: ${expectedPublicId}`);
    } else {
        console.log(`❌ ${pageKey}: 未找到配置`);
    }
}

// 测试总结
console.log('\n===============');
console.log('📊 测试总结');
console.log('===============');
console.log(`HybridCDNManager: ${testsPassed}/${totalTests} 通过`);
console.log(`CloudinaryLoadBalancer: ${loadBalancerPassed}/${totalTests} 通过`);

const hybridSuccess = testsPassed === totalTests;
const loadBalancerSuccess = loadBalancerPassed === totalTests;

if (hybridSuccess && loadBalancerSuccess) {
    console.log('\n🎉 所有映射测试通过！');
    console.log('\n✅ 修复完成:');
    console.log('  • HybridCDNManager映射已修复');
    console.log('  • CloudinaryLoadBalancer配置已修复');
    console.log('  • 所有页面键都正确映射到Cloudinary资源');
    console.log('\n🌐 现在所有页面都应该能从Cloudinary加载视频');
    console.log('\n🔗 测试链接:');
    console.log('  • 主页: https://love.yuh.cool/');
    console.log('  • 测试页面: https://love.yuh.cool/test-fix.html');
    console.log('  • 监控面板: https://love.yuh.cool/test/cdn-monitoring-dashboard.html');
} else {
    console.log('\n⚠️  发现问题:');
    if (!hybridSuccess) {
        console.log('  • HybridCDNManager映射需要修复');
    }
    if (!loadBalancerSuccess) {
        console.log('  • CloudinaryLoadBalancer配置需要修复');
    }
}

// 生成Cloudinary URL测试
console.log('\n🔗 生成的Cloudinary URL:');
for (const [pageKey, cloudinaryPath] of Object.entries(expectedMappings)) {
    const url = `https://res.cloudinary.com/dcglebc2w/video/upload/${cloudinaryPath}.mp4`;
    console.log(`  ${pageKey}: ${url}`);
}

console.log('\n✨ 测试完成！');
