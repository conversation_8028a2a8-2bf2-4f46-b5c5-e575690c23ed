#!/bin/bash

# 视频加载修复验证脚本
# 验证所有页面的视频是否能正常从Cloudinary加载

echo "🔧 视频加载修复验证脚本"
echo "================================"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 测试配置
CLOUDINARY_BASE="https://res.cloudinary.com/dcglebc2w/video/upload"
SITE_BASE="https://love.yuh.cool"

# 视频映射
declare -A videos=(
    ["INDEX"]="love-website/home"
    ["MEETINGS"]="love-website/meetings"
    ["ANNIVERSARY"]="love-website/anniversary"
    ["MEMORIAL"]="love-website/memorial"
    ["TOGETHER_DAYS"]="love-website/together-days"
)

declare -A pages=(
    ["INDEX"]="index.html"
    ["MEETINGS"]="meetings.html"
    ["ANNIVERSARY"]="anniversary.html"
    ["MEMORIAL"]="memorial.html"
    ["TOGETHER_DAYS"]="together-days.html"
)

# 计数器
total_tests=0
passed_tests=0
failed_tests=0

# 测试函数
test_url() {
    local url=$1
    local name=$2
    local timeout=${3:-10}
    
    echo -n "  测试 $name... "
    
    # 使用curl测试URL
    if timeout $timeout curl -s -f -I "$url" > /dev/null 2>&1; then
        echo -e "${GREEN}✅ 通过${NC}"
        ((passed_tests++))
        return 0
    else
        echo -e "${RED}❌ 失败${NC}"
        ((failed_tests++))
        return 1
    fi
}

# 测试页面可访问性
test_page_accessibility() {
    echo -e "${BLUE}📄 测试页面可访问性${NC}"
    
    for page_key in "${!pages[@]}"; do
        local page_url="$SITE_BASE/${pages[$page_key]}"
        ((total_tests++))
        test_url "$page_url" "${pages[$page_key]}"
    done
    echo
}

# 测试Cloudinary视频
test_cloudinary_videos() {
    echo -e "${BLUE}☁️  测试Cloudinary视频${NC}"
    
    for page_key in "${!videos[@]}"; do
        local video_url="$CLOUDINARY_BASE/${videos[$page_key]}.mp4"
        ((total_tests++))
        test_url "$video_url" "$page_key (${videos[$page_key]})" 15
    done
    echo
}

# 测试本地备用视频
test_local_videos() {
    echo -e "${BLUE}📁 测试本地备用视频${NC}"
    
    declare -A local_paths=(
        ["INDEX"]="/background/cloudinary-ready/home.mp4"
        ["MEETINGS"]="/background/cloudinary-ready/meetings.mp4"
        ["ANNIVERSARY"]="/background/cloudinary-ready/anniversary.mp4"
        ["MEMORIAL"]="/background/cloudinary-ready/memorial.mp4"
        ["TOGETHER_DAYS"]="/background/cloudinary-ready/together-days.mp4"
    )
    
    for page_key in "${!local_paths[@]}"; do
        local local_url="$SITE_BASE${local_paths[$page_key]}"
        ((total_tests++))
        test_url "$local_url" "$page_key (本地备用)" 10
    done
    echo
}

# 测试JavaScript文件
test_javascript_files() {
    echo -e "${BLUE}📜 测试JavaScript文件${NC}"
    
    local js_files=(
        "/config.js"
        "/hybrid-cdn-manager.js"
        "/cloudinary-load-balancer.js"
        "/simple-video-manager.js"
    )
    
    for js_file in "${js_files[@]}"; do
        local js_url="$SITE_BASE$js_file"
        ((total_tests++))
        test_url "$js_url" "$(basename $js_file)"
    done
    echo
}

# 测试修复页面
test_fix_page() {
    echo -e "${BLUE}🔧 测试修复页面${NC}"
    
    local fix_url="$SITE_BASE/test-fix.html"
    ((total_tests++))
    test_url "$fix_url" "test-fix.html"
    echo
}

# 高级测试：检查视频文件大小
test_video_sizes() {
    echo -e "${BLUE}📊 检查视频文件大小${NC}"
    
    for page_key in "${!videos[@]}"; do
        local video_url="$CLOUDINARY_BASE/${videos[$page_key]}.mp4"
        echo -n "  检查 $page_key 大小... "
        
        local size=$(curl -s -I "$video_url" | grep -i content-length | awk '{print $2}' | tr -d '\r')
        if [[ -n "$size" && "$size" -gt 0 ]]; then
            local size_mb=$((size / 1024 / 1024))
            echo -e "${GREEN}${size_mb}MB${NC}"
        else
            echo -e "${YELLOW}未知${NC}"
        fi
    done
    echo
}

# 生成测试报告
generate_report() {
    echo "================================"
    echo -e "${BLUE}📊 测试报告${NC}"
    echo "================================"
    echo "总测试数: $total_tests"
    echo -e "通过测试: ${GREEN}$passed_tests${NC}"
    echo -e "失败测试: ${RED}$failed_tests${NC}"
    
    local success_rate=$((passed_tests * 100 / total_tests))
    echo "成功率: $success_rate%"
    echo
    
    if [ $failed_tests -eq 0 ]; then
        echo -e "${GREEN}🎉 所有测试都通过了！视频加载修复成功！${NC}"
        echo
        echo "✅ Cloudinary CDN连接正常"
        echo "✅ 所有页面可访问"
        echo "✅ 本地备用文件可用"
        echo "✅ JavaScript文件加载正常"
        echo
        echo "🌐 你可以访问以下页面测试："
        echo "   • 主页: $SITE_BASE/"
        echo "   • 测试页面: $SITE_BASE/test-fix.html"
        echo "   • 监控面板: $SITE_BASE/test/cdn-monitoring-dashboard.html"
    else
        echo -e "${YELLOW}⚠️  发现 $failed_tests 个问题，需要进一步检查${NC}"
        echo
        echo "建议检查："
        echo "1. 网络连接是否正常"
        echo "2. Cloudinary配置是否正确"
        echo "3. 服务器文件是否完整"
    fi
}

# 主函数
main() {
    echo "开始验证视频加载修复..."
    echo
    
    # 运行所有测试
    test_page_accessibility
    test_cloudinary_videos
    test_local_videos
    test_javascript_files
    test_fix_page
    test_video_sizes
    
    # 生成报告
    generate_report
}

# 运行主函数
main
