#!/usr/bin/env node

/**
 * 视频加载测试脚本
 * 测试所有页面的视频是否能正常从Cloudinary加载
 */

const https = require('https');
const http = require('http');

// 测试配置
const CLOUDINARY_BASE = 'https://res.cloudinary.com/dcglebc2w/video/upload';
const LOCAL_BASE = 'https://love.yuh.cool';

// 视频映射
const videoMappings = {
    'INDEX': {
        cloudinary: 'love-website/home',
        local: '/background/cloudinary-ready/home.mp4',
        page: 'index.html'
    },
    'MEETINGS': {
        cloudinary: 'love-website/meetings',
        local: '/background/cloudinary-ready/meetings.mp4',
        page: 'meetings.html'
    },
    'ANNIVERSARY': {
        cloudinary: 'love-website/anniversary',
        local: '/background/cloudinary-ready/anniversary.mp4',
        page: 'anniversary.html'
    },
    'MEMORIAL': {
        cloudinary: 'love-website/memorial',
        local: '/background/cloudinary-ready/memorial.mp4',
        page: 'memorial.html'
    },
    'TOGETHER_DAYS': {
        cloudinary: 'love-website/together-days',
        local: '/background/cloudinary-ready/together-days.mp4',
        page: 'together-days.html'
    }
};

// 测试URL是否可访问
function testUrl(url) {
    return new Promise((resolve) => {
        const client = url.startsWith('https:') ? https : http;
        
        const req = client.request(url, { method: 'HEAD' }, (res) => {
            resolve({
                url,
                status: res.statusCode,
                success: res.statusCode === 200,
                size: res.headers['content-length'] || 'unknown'
            });
        });
        
        req.on('error', (error) => {
            resolve({
                url,
                status: 'ERROR',
                success: false,
                error: error.message
            });
        });
        
        req.setTimeout(10000, () => {
            req.destroy();
            resolve({
                url,
                status: 'TIMEOUT',
                success: false,
                error: 'Request timeout'
            });
        });
        
        req.end();
    });
}

// 测试页面是否可访问
function testPage(pageUrl) {
    return new Promise((resolve) => {
        const req = https.request(pageUrl, { method: 'HEAD' }, (res) => {
            resolve({
                url: pageUrl,
                status: res.statusCode,
                success: res.statusCode === 200
            });
        });
        
        req.on('error', (error) => {
            resolve({
                url: pageUrl,
                status: 'ERROR',
                success: false,
                error: error.message
            });
        });
        
        req.setTimeout(5000, () => {
            req.destroy();
            resolve({
                url: pageUrl,
                status: 'TIMEOUT',
                success: false,
                error: 'Request timeout'
            });
        });
        
        req.end();
    });
}

// 主测试函数
async function runTests() {
    console.log('🚀 开始视频加载测试...\n');
    
    const results = {
        cloudinary: {},
        local: {},
        pages: {},
        summary: {
            total: 0,
            cloudinarySuccess: 0,
            localSuccess: 0,
            pageSuccess: 0
        }
    };
    
    // 测试Cloudinary视频
    console.log('📡 测试Cloudinary CDN视频...');
    for (const [pageKey, mapping] of Object.entries(videoMappings)) {
        const cloudinaryUrl = `${CLOUDINARY_BASE}/${mapping.cloudinary}.mp4`;
        const result = await testUrl(cloudinaryUrl);
        results.cloudinary[pageKey] = result;
        results.summary.total++;
        
        if (result.success) {
            results.summary.cloudinarySuccess++;
            console.log(`✅ ${pageKey}: ${result.status} (${result.size} bytes)`);
        } else {
            console.log(`❌ ${pageKey}: ${result.status} - ${result.error || 'Failed'}`);
        }
    }
    
    console.log('\n📁 测试本地备用视频...');
    for (const [pageKey, mapping] of Object.entries(videoMappings)) {
        const localUrl = `${LOCAL_BASE}${mapping.local}`;
        const result = await testUrl(localUrl);
        results.local[pageKey] = result;
        
        if (result.success) {
            results.summary.localSuccess++;
            console.log(`✅ ${pageKey}: ${result.status} (${result.size} bytes)`);
        } else {
            console.log(`❌ ${pageKey}: ${result.status} - ${result.error || 'Failed'}`);
        }
    }
    
    console.log('\n🌐 测试页面可访问性...');
    for (const [pageKey, mapping] of Object.entries(videoMappings)) {
        const pageUrl = `${LOCAL_BASE}/${mapping.page}`;
        const result = await testPage(pageUrl);
        results.pages[pageKey] = result;
        
        if (result.success) {
            results.summary.pageSuccess++;
            console.log(`✅ ${mapping.page}: ${result.status}`);
        } else {
            console.log(`❌ ${mapping.page}: ${result.status} - ${result.error || 'Failed'}`);
        }
    }
    
    // 输出总结
    console.log('\n📊 测试总结:');
    console.log(`总页面数: ${results.summary.total}`);
    console.log(`Cloudinary成功: ${results.summary.cloudinarySuccess}/${results.summary.total}`);
    console.log(`本地文件成功: ${results.summary.localSuccess}/${results.summary.total}`);
    console.log(`页面可访问: ${results.summary.pageSuccess}/${results.summary.total}`);
    
    const cloudinaryRate = (results.summary.cloudinarySuccess / results.summary.total * 100).toFixed(1);
    const localRate = (results.summary.localSuccess / results.summary.total * 100).toFixed(1);
    const pageRate = (results.summary.pageSuccess / results.summary.total * 100).toFixed(1);
    
    console.log(`\nCloudinary成功率: ${cloudinaryRate}%`);
    console.log(`本地文件成功率: ${localRate}%`);
    console.log(`页面可访问率: ${pageRate}%`);
    
    // 检查是否需要修复
    if (results.summary.cloudinarySuccess < results.summary.total) {
        console.log('\n⚠️  发现Cloudinary视频加载问题，建议检查:');
        console.log('1. Cloudinary API配置');
        console.log('2. 视频文件是否已正确上传');
        console.log('3. 网络连接是否正常');
    }
    
    if (results.summary.cloudinarySuccess === results.summary.total) {
        console.log('\n🎉 所有Cloudinary视频都可以正常访问！');
    }
    
    return results;
}

// 运行测试
if (require.main === module) {
    runTests().catch(console.error);
}

module.exports = { runTests, testUrl, videoMappings };
