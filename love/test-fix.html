<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>视频加载修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        .video-test {
            width: 300px;
            height: 200px;
            border: 2px solid #ddd;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 视频加载修复测试</h1>
        <p>测试修复后的视频加载系统是否正常工作</p>
        
        <div id="status" class="status info">
            准备开始测试...
        </div>
        
        <button onclick="testAllPages()">测试所有页面</button>
        <button onclick="testCloudinaryDirect()">直接测试Cloudinary</button>
        <button onclick="clearLog()">清空日志</button>
        
        <h3>测试日志:</h3>
        <div id="log" class="log"></div>
        
        <h3>视频测试区域:</h3>
        <video id="testVideo" class="video-test" controls muted></video>
    </div>

    <!-- 加载必要的脚本 -->
    <script src="/config.js"></script>
    <script src="/hybrid-cdn-manager.js"></script>
    <script src="/simple-video-manager.js"></script>
    
    <script>
        let logElement = document.getElementById('log');
        let statusElement = document.getElementById('status');
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logMessage = `[${timestamp}] ${message}\n`;
            logElement.textContent += logMessage;
            logElement.scrollTop = logElement.scrollHeight;
            
            // 同时输出到控制台
            console.log(message);
        }
        
        function setStatus(message, type = 'info') {
            statusElement.textContent = message;
            statusElement.className = `status ${type}`;
        }
        
        function clearLog() {
            logElement.textContent = '';
        }
        
        // 测试所有页面的视频映射
        async function testAllPages() {
            setStatus('正在测试所有页面...', 'info');
            log('🚀 开始测试所有页面的视频映射');
            
            const pages = ['INDEX', 'MEETINGS', 'ANNIVERSARY', 'MEMORIAL', 'TOGETHER_DAYS'];
            let successCount = 0;
            
            // 检查HybridCDNManager是否可用
            if (typeof HybridCDNManager === 'undefined') {
                log('❌ HybridCDNManager未加载', 'error');
                setStatus('HybridCDNManager未加载', 'error');
                return;
            }
            
            const manager = new HybridCDNManager();
            log('✅ HybridCDNManager已初始化');
            
            for (const pageKey of pages) {
                try {
                    log(`🔍 测试页面: ${pageKey}`);
                    
                    // 检查映射是否存在
                    const mapping = manager.videoMappings[pageKey];
                    if (!mapping) {
                        log(`❌ ${pageKey}: 未找到视频映射`, 'error');
                        continue;
                    }
                    
                    log(`✅ ${pageKey}: 找到映射 - Cloudinary: ${mapping.cloudinary}`);
                    
                    // 测试选择最佳源
                    const source = manager.selectBestSource(pageKey);
                    if (source) {
                        log(`✅ ${pageKey}: 选择源 - ${source.id} (${source.type})`);
                        
                        // 生成URL
                        const url = manager.generateVideoUrl(pageKey, source);
                        if (url) {
                            log(`✅ ${pageKey}: 生成URL - ${url}`);
                            successCount++;
                        } else {
                            log(`❌ ${pageKey}: URL生成失败`, 'error');
                        }
                    } else {
                        log(`❌ ${pageKey}: 无可用源`, 'error');
                    }
                    
                } catch (error) {
                    log(`❌ ${pageKey}: 测试失败 - ${error.message}`, 'error');
                }
            }
            
            const successRate = (successCount / pages.length * 100).toFixed(1);
            log(`\n📊 测试完成: ${successCount}/${pages.length} 成功 (${successRate}%)`);
            
            if (successCount === pages.length) {
                setStatus(`✅ 所有页面测试通过! (${successCount}/${pages.length})`, 'success');
            } else {
                setStatus(`⚠️ 部分页面测试失败 (${successCount}/${pages.length})`, 'error');
            }
        }
        
        // 直接测试Cloudinary连接
        async function testCloudinaryDirect() {
            setStatus('正在测试Cloudinary连接...', 'info');
            log('🌐 开始直接测试Cloudinary连接');
            
            const testVideo = document.getElementById('testVideo');
            const cloudinaryUrl = 'https://res.cloudinary.com/dcglebc2w/video/upload/love-website/home.mp4';
            
            try {
                log(`🔗 测试URL: ${cloudinaryUrl}`);
                
                testVideo.src = cloudinaryUrl;
                
                // 监听加载事件
                testVideo.onloadeddata = () => {
                    log('✅ Cloudinary视频加载成功!');
                    setStatus('✅ Cloudinary连接正常', 'success');
                };
                
                testVideo.onerror = (error) => {
                    log(`❌ Cloudinary视频加载失败: ${error.message || 'Unknown error'}`, 'error');
                    setStatus('❌ Cloudinary连接失败', 'error');
                };
                
                // 设置超时
                setTimeout(() => {
                    if (testVideo.readyState < 2) {
                        log('⏰ Cloudinary视频加载超时', 'error');
                        setStatus('⏰ 连接超时', 'error');
                    }
                }, 10000);
                
            } catch (error) {
                log(`❌ 测试失败: ${error.message}`, 'error');
                setStatus('❌ 测试失败', 'error');
            }
        }
        
        // 页面加载完成后自动运行基础测试
        document.addEventListener('DOMContentLoaded', function() {
            log('📄 页面加载完成');
            
            // 检查必要的对象是否存在
            setTimeout(() => {
                if (typeof window.CONFIG !== 'undefined') {
                    log('✅ CONFIG已加载');
                } else {
                    log('❌ CONFIG未加载', 'error');
                }
                
                if (typeof HybridCDNManager !== 'undefined') {
                    log('✅ HybridCDNManager已加载');
                } else {
                    log('❌ HybridCDNManager未加载', 'error');
                }
                
                if (typeof SimpleVideoManager !== 'undefined') {
                    log('✅ SimpleVideoManager已加载');
                } else {
                    log('❌ SimpleVideoManager未加载', 'error');
                }
                
                log('🎯 准备就绪，可以开始测试');
                setStatus('准备就绪，点击按钮开始测试', 'info');
            }, 1000);
        });
    </script>
</body>
</html>
