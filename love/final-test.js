#!/usr/bin/env node

/**
 * 最终视频加载测试
 * 模拟浏览器环境测试修复后的视频映射功能
 */

// 模拟浏览器环境
global.window = {
    location: { pathname: '/' },
    innerWidth: 1920,
    innerHeight: 1080,
    devicePixelRatio: 1,
    navigator: {
        userAgent: 'Mozilla/5.0 (Test Environment)',
        connection: {
            effectiveType: '4g',
            downlink: 10
        }
    },
    performance: {
        now: () => Date.now()
    },
    document: {
        createElement: () => ({
            style: {},
            addEventListener: () => {},
            load: () => {},
            play: () => Promise.resolve()
        }),
        addEventListener: () => {},
        head: { appendChild: () => {} },
        body: { appendChild: () => {} }
    },
    console: console
};

// 加载配置文件
const fs = require('fs');
const path = require('path');

// 读取并执行config.js
const configPath = path.join(__dirname, 'config.js');
const configContent = fs.readFileSync(configPath, 'utf8');
eval(configContent);

// 读取并执行hybrid-cdn-manager.js
const hybridManagerPath = path.join(__dirname, 'hybrid-cdn-manager.js');
const hybridManagerContent = fs.readFileSync(hybridManagerPath, 'utf8');
eval(hybridManagerContent);

// 读取并执行cloudinary-load-balancer.js
const loadBalancerPath = path.join(__dirname, 'cloudinary-load-balancer.js');
const loadBalancerContent = fs.readFileSync(loadBalancerPath, 'utf8');
eval(loadBalancerContent);

// 测试函数
function testVideoMappings() {
    console.log('🔧 最终视频加载测试');
    console.log('===================');
    
    const testPages = ['INDEX', 'MEETINGS', 'ANNIVERSARY', 'MEMORIAL', 'TOGETHER_DAYS'];
    let totalTests = 0;
    let passedTests = 0;
    
    console.log('\n📊 测试HybridCDNManager映射...');
    
    try {
        const hybridManager = new global.window.HybridCDNManager();
        
        for (const pageKey of testPages) {
            totalTests++;
            console.log(`\n🔍 测试页面: ${pageKey}`);
            
            // 测试映射是否存在
            const mapping = hybridManager.videoMappings[pageKey];
            if (mapping) {
                console.log(`  ✅ 映射存在: ${mapping.cloudinary}`);
                
                // 测试源选择
                const source = hybridManager.selectBestSource(pageKey);
                if (source) {
                    console.log(`  ✅ 源选择成功: ${source.id} (${source.type})`);
                    
                    // 测试URL生成
                    const url = hybridManager.generateVideoUrl(pageKey, source);
                    if (url) {
                        console.log(`  ✅ URL生成成功: ${url}`);
                        passedTests++;
                    } else {
                        console.log(`  ❌ URL生成失败`);
                    }
                } else {
                    console.log(`  ❌ 源选择失败`);
                }
            } else {
                console.log(`  ❌ 映射不存在`);
            }
        }
        
    } catch (error) {
        console.log(`❌ HybridCDNManager测试失败: ${error.message}`);
    }
    
    console.log('\n📊 测试CloudinaryLoadBalancer映射...');
    
    try {
        const loadBalancer = new global.window.CloudinaryLoadBalancer();
        
        for (const pageKey of testPages) {
            console.log(`\n🔍 测试页面: ${pageKey}`);
            
            // 测试配置是否存在
            const config = loadBalancer.videoConfigs[pageKey];
            if (config) {
                console.log(`  ✅ 配置存在: ${config.publicId}`);
                
                // 测试URL生成
                try {
                    const provider = loadBalancer.getNextProvider();
                    if (provider) {
                        const url = loadBalancer.generateVideoUrl(pageKey, provider);
                        if (url) {
                            console.log(`  ✅ URL生成成功: ${url}`);
                        } else {
                            console.log(`  ❌ URL生成失败`);
                        }
                    } else {
                        console.log(`  ❌ 无可用提供商`);
                    }
                } catch (error) {
                    console.log(`  ❌ URL生成异常: ${error.message}`);
                }
            } else {
                console.log(`  ❌ 配置不存在`);
            }
        }
        
    } catch (error) {
        console.log(`❌ CloudinaryLoadBalancer测试失败: ${error.message}`);
    }
    
    // 测试总结
    console.log('\n===================');
    console.log('📊 测试总结');
    console.log('===================');
    console.log(`HybridCDNManager测试: ${passedTests}/${totalTests} 通过`);
    
    const successRate = totalTests > 0 ? (passedTests / totalTests * 100).toFixed(1) : 0;
    console.log(`成功率: ${successRate}%`);
    
    if (passedTests === totalTests) {
        console.log('\n🎉 所有测试通过！视频映射修复成功！');
        console.log('\n✅ 修复内容:');
        console.log('  • 添加了大写页面键映射 (INDEX, MEETINGS, etc.)');
        console.log('  • 保持了向后兼容的小写键映射');
        console.log('  • 修复了HybridCDNManager和CloudinaryLoadBalancer');
        console.log('\n🌐 现在所有页面都应该能正常从Cloudinary加载视频');
    } else {
        console.log(`\n⚠️  发现问题，需要进一步检查`);
    }
    
    return { totalTests, passedTests, successRate };
}

// 测试页面键转换
function testPageKeyConversion() {
    console.log('\n🔄 测试页面键转换...');
    
    const pathMappings = {
        '/': 'INDEX',
        '/index.html': 'INDEX',
        '/meetings': 'MEETINGS',
        '/meetings.html': 'MEETINGS',
        '/anniversary': 'ANNIVERSARY',
        '/anniversary.html': 'ANNIVERSARY',
        '/memorial': 'MEMORIAL',
        '/memorial.html': 'MEMORIAL',
        '/together-days': 'TOGETHER_DAYS',
        '/together-days.html': 'TOGETHER_DAYS'
    };
    
    // 模拟getCurrentPageKey函数
    function getCurrentPageKey(pathname) {
        if (pathname === '/' || pathname === '/index.html') return 'INDEX';
        if (pathname === '/meetings' || pathname === '/meetings.html') return 'MEETINGS';
        if (pathname === '/anniversary' || pathname === '/anniversary.html') return 'ANNIVERSARY';
        if (pathname === '/memorial' || pathname === '/memorial.html') return 'MEMORIAL';
        if (pathname === '/together-days' || pathname === '/together-days.html') return 'TOGETHER_DAYS';
        return 'INDEX';
    }
    
    let conversionTests = 0;
    let conversionPassed = 0;
    
    for (const [path, expectedKey] of Object.entries(pathMappings)) {
        conversionTests++;
        const actualKey = getCurrentPageKey(path);
        
        if (actualKey === expectedKey) {
            console.log(`  ✅ ${path} → ${actualKey}`);
            conversionPassed++;
        } else {
            console.log(`  ❌ ${path} → ${actualKey} (期望: ${expectedKey})`);
        }
    }
    
    console.log(`\n页面键转换测试: ${conversionPassed}/${conversionTests} 通过`);
    
    return { conversionTests, conversionPassed };
}

// 主函数
function main() {
    const mappingResults = testVideoMappings();
    const conversionResults = testPageKeyConversion();
    
    console.log('\n🏁 最终测试完成');
    console.log('================');
    console.log(`映射测试: ${mappingResults.passedTests}/${mappingResults.totalTests} (${mappingResults.successRate}%)`);
    console.log(`转换测试: ${conversionResults.conversionPassed}/${conversionResults.conversionTests}`);
    
    const allPassed = mappingResults.passedTests === mappingResults.totalTests && 
                     conversionResults.conversionPassed === conversionResults.conversionTests;
    
    if (allPassed) {
        console.log('\n🎊 恭喜！所有测试都通过了！');
        console.log('现在可以访问 https://love.yuh.cool/ 查看修复效果');
    } else {
        console.log('\n⚠️  仍有问题需要解决');
    }
}

// 运行测试
if (require.main === module) {
    main();
}
