/**
 * 混合CDN管理器
 * 结合Cloudinary + 本地文件 + 其他CDN的智能负载均衡
 */

class HybridCDNManager {
    constructor() {
        // 多种CDN源配置
        this.sources = [
            {
                id: 'cloudinary-primary',
                type: 'cloudinary',
                cloudName: 'dcglebc2w',
                baseUrl: 'https://res.cloudinary.com/dcglebc2w',
                priority: 1,
                quota: 20, // GB/月
                used: 0,
                status: 'active'
            },
            {
                id: 'cloudinary-secondary',
                type: 'cloudinary', 
                cloudName: 'your-second-account', // 需要第二个账号
                baseUrl: 'https://res.cloudinary.com/your-second-account',
                priority: 2,
                quota: 20,
                used: 0,
                status: 'standby' // 备用
            },
            {
                id: 'local-optimized',
                type: 'local',
                baseUrl: '/background/cloudinary-ready',
                priority: 2, // 提高优先级，作为Cloudinary的直接备选
                quota: Infinity, // 无限制
                used: 0,
                status: 'active'
            },
            {
                id: 'local-original',
                type: 'local',
                baseUrl: '/background',
                priority: 4,
                quota: Infinity,
                used: 0,
                status: 'fallback' // 最后备选
            }
        ];

        // 视频文件映射 - 修复页面键匹配问题
        this.videoMappings = {
            // 使用大写键匹配simple-video-manager.js中的getCurrentPageKey()
            'INDEX': {
                cloudinary: 'love-website/home',
                local: 'home/home.mp4',
                localOptimized: 'home.mp4'
            },
            'MEETINGS': {
                cloudinary: 'love-website/meetings',
                local: 'meetings/meetings.mp4',
                localOptimized: 'meetings.mp4'
            },
            'ANNIVERSARY': {
                cloudinary: 'love-website/anniversary',
                local: 'anniversary/anniversary.mp4',
                localOptimized: 'anniversary.mp4'
            },
            'MEMORIAL': {
                cloudinary: 'love-website/memorial',
                local: 'memorial/memorial.mp4',
                localOptimized: 'memorial.mp4'
            },
            'TOGETHER_DAYS': {
                cloudinary: 'love-website/together-days',
                local: 'together-days/together-days.mp4',
                localOptimized: 'together-days.mp4'
            },
            // 保持向后兼容的小写键
            'home': {
                cloudinary: 'love-website/home',
                local: 'home/home.mp4',
                localOptimized: 'home.mp4'
            },
            'meetings': {
                cloudinary: 'love-website/meetings',
                local: 'meetings/meetings.mp4',
                localOptimized: 'meetings.mp4'
            },
            'anniversary': {
                cloudinary: 'love-website/anniversary',
                local: 'anniversary/anniversary.mp4',
                localOptimized: 'anniversary.mp4'
            },
            'together-days': {
                cloudinary: 'love-website/together-days',
                local: 'together-days/together-days.mp4',
                localOptimized: 'together-days.mp4'
            },
            'memorial': {
                cloudinary: 'love-website/memorial',
                local: 'memorial/memorial.mp4',
                localOptimized: 'memorial.mp4'
            }
        };

        // 智能选择策略
        this.strategy = 'smart'; // 'smart', 'quota-aware', 'performance'
        this.performanceStats = new Map();
    }

    // 智能选择最佳CDN源
    selectBestSource(pageName, userContext = {}) {
        const availableSources = this.sources.filter(s => 
            s.status === 'active' || s.status === 'standby'
        );

        // 强制Cloudinary优先策略
        availableSources.sort((a, b) => {
            // 1. Cloudinary类型始终优先于本地文件
            if (a.type === 'cloudinary' && b.type !== 'cloudinary') return -1;
            if (b.type === 'cloudinary' && a.type !== 'cloudinary') return 1;

            // 2. 检查配额（只对Cloudinary）
            if (a.type === 'cloudinary' && a.used >= a.quota) return 1;
            if (b.type === 'cloudinary' && b.used >= b.quota) return -1;

            // 3. 同类型内按优先级排序
            if (a.type === b.type) {
                return a.priority - b.priority;
            }

            // 4. 最后按基础优先级
            return a.priority - b.priority;
        });

        const selected = availableSources[0];
        console.log(`🎯 选择CDN源: ${selected.id} (${selected.type})`);
        return selected;
    }

    // 生成视频URL
    generateVideoUrl(pageName, source, userContext = {}) {
        const mapping = this.videoMappings[pageName];
        if (!mapping) {
            console.error(`未找到页面 ${pageName} 的视频映射`);
            return null;
        }

        let url;
        switch (source.type) {
            case 'cloudinary':
                const transforms = this.getCloudinaryTransforms(userContext);
                url = `${source.baseUrl}/video/upload/${transforms}/${mapping.cloudinary}.mp4`;
                break;
                
            case 'local':
                if (source.id === 'local-optimized') {
                    // 使用压缩后的文件
                    url = `${source.baseUrl}/${mapping.localOptimized}`;
                } else if (source.id === 'local-original') {
                    // 只有在最后备选时才使用原始文件
                    url = `${source.baseUrl}/${mapping.local}`;
                } else {
                    // 默认优先使用压缩文件
                    url = `/background/cloudinary-ready/${mapping.localOptimized}`;
                }
                break;
                
            default:
                console.error(`未知的源类型: ${source.type}`);
                return null;
        }

        return url;
    }

    // 获取Cloudinary变换参数
    getCloudinaryTransforms(userContext) {
        const { isMobile, isTablet, isSlowConnection } = this.analyzeUserContext(userContext);

        if (isSlowConnection) {
            return 'q_auto:eco,f_auto,w_1280,h_720,c_limit';
        }
        
        if (isMobile) {
            return 'q_auto:good,f_auto,w_1920,h_1080,c_limit';
        }
        
        if (isTablet) {
            return 'q_auto:best,f_auto,w_2048,h_1152,c_limit';
        }
        
        return 'q_auto:best,f_auto,w_2560,h_1440,c_limit';
    }

    // 分析用户上下文
    analyzeUserContext(context = {}) {
        return {
            isMobile: context.isMobile || window.innerWidth <= 768,
            isTablet: context.isTablet || (window.innerWidth > 768 && window.innerWidth <= 1024),
            isSlowConnection: context.isSlowConnection || (
                navigator.connection && 
                (navigator.connection.effectiveType === 'slow-2g' || 
                 navigator.connection.effectiveType === '2g')
            ),
            userAgent: context.userAgent || navigator.userAgent
        };
    }

    // 智能加载视频（多源故障转移）
    async loadVideoBackground(pageName, videoElement, userContext = {}) {
        const maxAttempts = Math.min(this.sources.length, 3); // 最多尝试3个源
        let attempts = 0;

        while (attempts < maxAttempts) {
            const source = this.selectBestSource(pageName, userContext);
            if (!source) break;

            const startTime = performance.now();
            
            try {
                const videoUrl = this.generateVideoUrl(pageName, source, userContext);
                if (!videoUrl) {
                    attempts++;
                    continue;
                }

                console.log(`🎬 尝试加载: ${pageName} from ${source.id}`);
                console.log(`📹 URL: ${videoUrl}`);

                // 设置加载超时
                const loadPromise = new Promise((resolve, reject) => {
                    const onLoad = () => {
                        videoElement.removeEventListener('error', onError);
                        resolve();
                    };
                    const onError = (e) => {
                        videoElement.removeEventListener('canplaythrough', onLoad);
                        reject(new Error(`视频加载错误: ${e.message || 'Unknown error'}`));
                    };
                    
                    videoElement.addEventListener('canplaythrough', onLoad, { once: true });
                    videoElement.addEventListener('error', onError, { once: true });
                    
                    // 超时处理
                    setTimeout(() => {
                        videoElement.removeEventListener('canplaythrough', onLoad);
                        videoElement.removeEventListener('error', onError);
                        reject(new Error('加载超时'));
                    }, 15000);
                });

                videoElement.src = videoUrl;
                videoElement.load();

                await loadPromise;

                // 成功加载
                const loadTime = performance.now() - startTime;
                this.recordPerformance(source.id, loadTime, true);
                
                // 更新使用量（估算）
                if (source.type === 'cloudinary') {
                    source.used += 0.1; // 估算每次加载消耗0.1GB
                }

                videoElement.style.opacity = '1';
                videoElement.play().catch(e => {
                    console.log('自动播放被阻止，等待用户交互');
                });

                console.log(`✅ 视频加载成功: ${pageName} from ${source.id} (${loadTime.toFixed(0)}ms)`);
                return { success: true, source: source.id, loadTime };

            } catch (error) {
                const loadTime = performance.now() - startTime;
                this.recordPerformance(source.id, loadTime, false);
                
                console.error(`❌ 源 ${source.id} 加载失败:`, error.message);

                // 记录失败，但不修改优先级，让下次重试时仍然优先Cloudinary
                console.log(`🔄 将尝试下一个源 (尝试 ${attempts + 1}/${maxAttempts})`);

                attempts++;
            }
        }

        // 所有源都失败
        console.error(`❌ 所有CDN源都失败，回退到渐变背景`);
        this.fallbackToGradient(videoElement);
        return { success: false, source: null, error: '所有源都失败' };
    }

    // 记录性能统计
    recordPerformance(sourceId, loadTime, success) {
        if (!this.performanceStats.has(sourceId)) {
            this.performanceStats.set(sourceId, {
                totalAttempts: 0,
                successCount: 0,
                totalLoadTime: 0,
                avgLoadTime: 0,
                successRate: 0
            });
        }

        const stats = this.performanceStats.get(sourceId);
        stats.totalAttempts++;
        if (success) {
            stats.successCount++;
            stats.totalLoadTime += loadTime;
        }
        
        stats.avgLoadTime = stats.totalLoadTime / stats.successCount || 0;
        stats.successRate = stats.successCount / stats.totalAttempts;
        
        console.log(`📊 ${sourceId} 性能统计:`, {
            成功率: `${(stats.successRate * 100).toFixed(1)}%`,
            平均加载时间: `${stats.avgLoadTime.toFixed(0)}ms`,
            总尝试次数: stats.totalAttempts
        });
    }

    // 失败时回退到渐变背景
    fallbackToGradient(videoElement) {
        const container = videoElement.parentElement;
        if (container) {
            container.style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
            videoElement.style.display = 'none';
        }
    }

    // 获取系统状态
    getSystemStatus() {
        return {
            sources: this.sources.map(s => ({
                id: s.id,
                type: s.type,
                status: s.status,
                priority: s.priority,
                quota: s.quota,
                used: s.used,
                usage: s.quota === Infinity ? 0 : (s.used / s.quota * 100).toFixed(1) + '%'
            })),
            performance: Object.fromEntries(this.performanceStats),
            strategy: this.strategy
        };
    }
}

// 全局初始化
window.HybridCDNManager = HybridCDNManager;
