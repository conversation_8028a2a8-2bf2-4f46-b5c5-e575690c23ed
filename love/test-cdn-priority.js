#!/usr/bin/env node

/**
 * CDN优先级测试脚本
 * 测试修复后的CDN选择逻辑和本地文件路径
 */

// 模拟浏览器环境
global.window = {
    location: { pathname: '/' },
    innerWidth: 1920,
    innerHeight: 1080,
    devicePixelRatio: 1,
    navigator: {
        userAgent: 'Mozilla/5.0 (Test Environment)',
        connection: {
            effectiveType: '4g',
            downlink: 10
        }
    },
    performance: {
        now: () => Date.now()
    },
    document: {
        createElement: () => ({
            style: {},
            addEventListener: () => {},
            load: () => {},
            play: () => Promise.resolve()
        }),
        addEventListener: () => {},
        head: { appendChild: () => {} },
        body: { appendChild: () => {} }
    },
    console: console
};

// 同时设置全局navigator
global.navigator = global.window.navigator;

// 读取并执行hybrid-cdn-manager.js
const fs = require('fs');
const hybridManagerContent = fs.readFileSync('hybrid-cdn-manager.js', 'utf8');
eval(hybridManagerContent);

console.log('🔧 CDN优先级和路径测试');
console.log('=======================');

function testCDNPriority() {
    console.log('\n📊 测试CDN源优先级...');
    
    const manager = new global.window.HybridCDNManager();
    const testPages = ['INDEX', 'MEETINGS', 'ANNIVERSARY', 'MEMORIAL', 'TOGETHER_DAYS'];
    
    console.log('\n🏗️ CDN源配置:');
    manager.sources.forEach(source => {
        console.log(`  ${source.id}: 优先级=${source.priority}, 类型=${source.type}, 状态=${source.status}`);
    });
    
    console.log('\n🎯 测试源选择逻辑:');
    for (const pageKey of testPages) {
        console.log(`\n📄 页面: ${pageKey}`);
        
        // 测试源选择
        const source = manager.selectBestSource(pageKey);
        if (source) {
            console.log(`  ✅ 选择源: ${source.id} (${source.type}, 优先级: ${source.priority})`);
            
            // 测试URL生成
            const url = manager.generateVideoUrl(pageKey, source);
            if (url) {
                console.log(`  🔗 生成URL: ${url}`);
                
                // 检查是否是Cloudinary
                if (source.type === 'cloudinary') {
                    console.log(`  ✅ 正确优先选择Cloudinary`);
                } else {
                    console.log(`  ⚠️  选择了非Cloudinary源: ${source.type}`);
                }
            } else {
                console.log(`  ❌ URL生成失败`);
            }
        } else {
            console.log(`  ❌ 源选择失败`);
        }
    }
}

function testLocalFilePaths() {
    console.log('\n📁 测试本地文件路径逻辑...');
    
    const manager = new global.window.HybridCDNManager();
    const testPages = ['INDEX', 'TOGETHER_DAYS']; // 重点测试together-days
    
    // 模拟不同的源
    const testSources = [
        { id: 'local-optimized', type: 'local', baseUrl: '/background/cloudinary-ready' },
        { id: 'local-original', type: 'local', baseUrl: '/background' },
        { id: 'local-other', type: 'local', baseUrl: '/some/path' }
    ];
    
    for (const pageKey of testPages) {
        console.log(`\n📄 页面: ${pageKey}`);
        
        for (const source of testSources) {
            const url = manager.generateVideoUrl(pageKey, source);
            console.log(`  ${source.id}: ${url}`);
            
            // 检查是否使用了压缩文件
            if (url && url.includes('cloudinary-ready')) {
                console.log(`    ✅ 使用压缩文件`);
            } else if (url && url.includes('/background/') && !url.includes('cloudinary-ready')) {
                console.log(`    ⚠️  使用原始大文件`);
            }
        }
    }
}

function testFailoverScenario() {
    console.log('\n🔄 测试故障转移场景...');
    
    const manager = new global.window.HybridCDNManager();
    
    // 模拟Cloudinary失败的情况
    console.log('\n模拟Cloudinary不可用:');
    manager.sources.forEach(source => {
        if (source.type === 'cloudinary') {
            source.status = 'inactive';
        }
    });
    
    const source = manager.selectBestSource('TOGETHER_DAYS');
    if (source) {
        console.log(`  故障转移选择: ${source.id} (${source.type})`);
        const url = manager.generateVideoUrl('TOGETHER_DAYS', source);
        console.log(`  故障转移URL: ${url}`);
        
        if (url && url.includes('cloudinary-ready')) {
            console.log(`  ✅ 正确使用压缩文件作为备选`);
        } else {
            console.log(`  ❌ 未使用压缩文件`);
        }
    }
    
    // 恢复Cloudinary状态
    manager.sources.forEach(source => {
        if (source.type === 'cloudinary') {
            source.status = 'active';
        }
    });
}

function testFileExistence() {
    console.log('\n📋 检查实际文件存在性...');
    
    const testFiles = [
        '/background/cloudinary-ready/together-days.mp4',
        '/background/together-days/together-days.mp4',
        '/background/cloudinary-ready/home.mp4',
        '/background/home/<USER>'
    ];
    
    for (const filePath of testFiles) {
        const fullPath = `.${filePath}`;
        try {
            const stats = fs.statSync(fullPath);
            const sizeMB = (stats.size / 1024 / 1024).toFixed(1);
            console.log(`  ✅ ${filePath}: ${sizeMB}MB`);
        } catch (error) {
            console.log(`  ❌ ${filePath}: 不存在`);
        }
    }
}

// 运行所有测试
function runAllTests() {
    testCDNPriority();
    testLocalFilePaths();
    testFailoverScenario();
    testFileExistence();
    
    console.log('\n🏁 测试完成');
    console.log('=============');
    console.log('\n✅ 修复内容:');
    console.log('  • Cloudinary源现在具有最高优先级');
    console.log('  • 本地备选优先使用压缩文件');
    console.log('  • 故障转移逻辑不会永久降低Cloudinary优先级');
    console.log('  • 本地优化文件优先级提升到2');
    console.log('\n🌐 现在所有页面都应该:');
    console.log('  1. 优先从Cloudinary加载视频');
    console.log('  2. Cloudinary失败时使用压缩的本地文件');
    console.log('  3. 只有在最后才使用原始大文件');
}

if (require.main === module) {
    runAllTests();
}
