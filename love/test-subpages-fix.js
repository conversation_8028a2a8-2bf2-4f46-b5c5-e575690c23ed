#!/usr/bin/env node

/**
 * 子页面修复验证脚本
 * 验证所有子页面是否已移除硬编码视频并正确集成VideoManager
 */

const fs = require('fs');

console.log('🔧 子页面视频加载修复验证');
console.log('==========================');

const pages = [
    { name: 'together-days', file: 'html/together-days.html', pageKey: 'TOGETHER_DAYS' },
    { name: 'anniversary', file: 'html/anniversary.html', pageKey: 'ANNIVERSARY' },
    { name: 'meetings', file: 'html/meetings.html', pageKey: 'MEETINGS' },
    { name: 'memorial', file: 'html/memorial.html', pageKey: 'MEMORIAL' }
];

let totalTests = 0;
let passedTests = 0;

function testResult(passed, message) {
    totalTests++;
    if (passed) {
        console.log(`✅ ${message}`);
        passedTests++;
    } else {
        console.log(`❌ ${message}`);
    }
}

console.log('\n📊 1. 检查硬编码视频标签是否已移除');
console.log('=====================================');

for (const page of pages) {
    try {
        const content = fs.readFileSync(page.file, 'utf8');
        
        // 检查是否还有硬编码的video source标签
        const hasHardcodedVideo = content.includes(`src="/background/${page.name}/${page.name}.mp4"`);
        
        testResult(!hasHardcodedVideo, `${page.name}.html - 硬编码视频标签已移除`);
        
        if (hasHardcodedVideo) {
            console.log(`  ⚠️  仍然包含: src="/background/${page.name}/${page.name}.mp4"`);
        }
        
    } catch (error) {
        testResult(false, `${page.name}.html - 文件读取失败: ${error.message}`);
    }
}

console.log('\n📊 2. 检查VideoManager集成');
console.log('==========================');

for (const page of pages) {
    try {
        const content = fs.readFileSync(page.file, 'utf8');
        
        // 检查是否引用了simple-video-manager.js
        const hasVideoManagerScript = content.includes('simple-video-manager.js');
        testResult(hasVideoManagerScript, `${page.name}.html - 引用了VideoManager脚本`);
        
        // 检查是否有VideoManager.loadVideo调用
        const hasLoadVideoCall = content.includes('VideoManager.loadVideo');
        testResult(hasLoadVideoCall, `${page.name}.html - 包含loadVideo调用`);
        
        // 检查是否使用了正确的页面键
        const hasCorrectPageKey = content.includes(`'${page.pageKey}'`);
        testResult(hasCorrectPageKey, `${page.name}.html - 使用正确的页面键 ${page.pageKey}`);
        
    } catch (error) {
        testResult(false, `${page.name}.html - VideoManager检查失败: ${error.message}`);
    }
}

console.log('\n📊 3. 检查视频容器结构');
console.log('======================');

for (const page of pages) {
    try {
        const content = fs.readFileSync(page.file, 'utf8');
        
        // 检查是否有video-background容器
        const hasVideoContainer = content.includes('class="video-background"');
        testResult(hasVideoContainer, `${page.name}.html - 包含video-background容器`);
        
        // 检查容器是否为空（由VideoManager动态填充）
        const containerMatch = content.match(/<div class="video-background"[^>]*>([\s\S]*?)<\/div>/);
        if (containerMatch) {
            const containerContent = containerMatch[1].trim();
            const isEmpty = !containerContent.includes('<video') && !containerContent.includes('<source');
            testResult(isEmpty, `${page.name}.html - video-background容器为空（由VideoManager管理）`);
        } else {
            testResult(false, `${page.name}.html - 未找到video-background容器`);
        }
        
    } catch (error) {
        testResult(false, `${page.name}.html - 容器检查失败: ${error.message}`);
    }
}

console.log('\n📊 4. 检查配置文件引用');
console.log('======================');

for (const page of pages) {
    try {
        const content = fs.readFileSync(page.file, 'utf8');
        
        // 检查是否引用了config.js
        const hasConfigScript = content.includes('src="/config.js"');
        testResult(hasConfigScript, `${page.name}.html - 引用了config.js`);
        
        // 检查脚本加载顺序（config.js应该在simple-video-manager.js之前）
        const configIndex = content.indexOf('src="/config.js"');
        const videoManagerIndex = content.indexOf('src="/simple-video-manager.js"');
        
        if (configIndex !== -1 && videoManagerIndex !== -1) {
            const correctOrder = configIndex < videoManagerIndex;
            testResult(correctOrder, `${page.name}.html - 脚本加载顺序正确`);
        } else {
            testResult(false, `${page.name}.html - 脚本引用不完整`);
        }
        
    } catch (error) {
        testResult(false, `${page.name}.html - 配置检查失败: ${error.message}`);
    }
}

console.log('\n==========================');
console.log('📊 测试总结');
console.log('==========================');
console.log(`总测试项: ${totalTests}`);
console.log(`通过测试: ${passedTests}`);
console.log(`失败测试: ${totalTests - passedTests}`);

const successRate = totalTests > 0 ? (passedTests / totalTests * 100).toFixed(1) : 0;
console.log(`成功率: ${successRate}%`);

if (passedTests === totalTests) {
    console.log('\n🎉 所有子页面修复验证通过！');
    console.log('\n✅ 修复内容:');
    console.log('  • 移除了所有硬编码的video标签');
    console.log('  • 所有页面都正确集成了VideoManager');
    console.log('  • 视频容器由VideoManager动态管理');
    console.log('  • 脚本加载顺序正确');
    console.log('\n🌐 现在子页面应该:');
    console.log('  1. 优先从Cloudinary加载视频');
    console.log('  2. Cloudinary失败时使用压缩的本地文件');
    console.log('  3. 不再直接加载原始大文件');
    console.log('  4. 加载速度显著提升，无卡顿');
    console.log('\n🔗 测试链接:');
    console.log('  • 在一起的日子: https://love.yuh.cool/together-days');
    console.log('  • 纪念日: https://love.yuh.cool/anniversary');
    console.log('  • 纪念物: https://love.yuh.cool/memorial');
    console.log('  • 每一次相遇: https://love.yuh.cool/meetings');
} else {
    console.log('\n⚠️  发现问题需要修复');
    console.log('\n建议检查:');
    console.log('  1. 确保所有硬编码video标签已移除');
    console.log('  2. 确保VideoManager正确集成');
    console.log('  3. 确保脚本加载顺序正确');
}

console.log('\n🧹 清理提示:');
console.log('测试完成后可以删除此脚本: rm test-subpages-fix.js');
